<template>
  <div>
    <!-- Results section -->
    <el-divider class="el-divider--nowrap">Step 2. Review and Edit Matched Tags</el-divider>
    <div v-if="results.length" class="results">
      <el-row justify="center" class="button-container">
        <el-col :span="24">
          <el-button plain round @click="toggleAllResults">
            <el-icon>
              <component :is="activeNames.length >= 1 ? ZoomOut : ZoomIn" />
            </el-icon>
            {{ activeNames.length >= 1 ? 'Collapse All' : 'Expand All' }}
          </el-button>
        </el-col>
      </el-row>
      <el-row justify="center">
        <el-col :span="24">
          <el-collapse v-model="activeNames">
            <el-collapse-item v-for="(result, index) in paginatedResults" :key="result.index" :name="result.index" @click="emitStateUpdated">
              <template #title>
                <div class="title-container">
                  <span class="truncate-title-results">
                    Item {{ result.index + 1 }}: {{ result.title }}
                    <el-icon v-if="isItemReviewed(result.index)" class="review-checkmark">
                      <CircleCheckFilled />
                    </el-icon>
                  </span>
                  <el-button
                    :type="isItemReviewed(result.index) ? 'warning' : 'success'"
                    size="small"
                    plain
                    @click.stop="toggleReviewStatus(result.index)"
                    class="review-button">
                    {{ isItemReviewed(result.index) ? 'Mark as NOT Reviewed' : 'Mark as Reviewed' }}
                  </el-button>
                </div>
              </template>
              <el-descriptions :column="1" border direction="vertical">
                <el-descriptions-item label="Title">
                  <el-text>{{ result.title }}</el-text>
                </el-descriptions-item>
                <el-descriptions-item label="Abstract">
                  <el-text>{{ getAbstractByIndex(result.index) }}</el-text>
                </el-descriptions-item>

                <el-descriptions-item>
                  <template #label>
                    Matched Tags
                    <el-tooltip effect="dark" :placement="screenIsPortrait ? 'top' : 'right'">
                      <template #content> This is the final list of tags for the item.<br />
                        You can click a tag to deselect it, add new tags using the input box, <br />or drag and
                        drop/double-click tags from other sections below to include them.</template>
                      <el-icon>
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip>
                  </template>

                  <div class="tags-container droppable-area" @dragover="handleDragOver"
                    @drop="handleDrop($event, result.index)">
                    <!-- Loading indicator for tag enhancement -->
                    <div v-if="!hasLoadedTags && isFetchingTags" class="tag-loading-indicator">
                      <el-icon class="is-loading"><Loading /></el-icon>
                      <span>Loading tag metadata...</span>
                    </div>

                    <!-- Existing matched tags with category-based colors -->
                    <el-tag v-for="tagData in getEnhancedMatchedTags(result)" :key="tagData.name"
                      :class="{
                        'deselected-tag': isTagDeselected(result.index, tagData.name),
                        [`category-${tagData.categoryClass}`]: true
                      }"
                      class="tag-item clickable-tag" @click="toggleTag(result.index, tagData.name)">
                      {{ tagData.name }}{{ tagData.count ? ` (${tagData.count})` : '' }}
                    </el-tag>

                    <!-- New tags with different styles -->
                    <template v-for="tag in Array.from(newTags.get(result.index) || [])" :key="tag.text">
                      <el-tag :type="tag.isMatched ? 'primary' : 'warning'" closable
                        @close="removeNewTag(result.index, tag.text)">
                        {{ tag.text }}
                      </el-tag>
                    </template>

                    <!-- Enhanced tag input with suggestions -->
                    <div class="tag-input-container">
                      <form @submit.prevent="() => addNewTag(result.index)">
                      <el-input class="tag-input" placeholder="Type to search or Enter to add customized tags..."
                        v-model="inputNewTagValue" @focus="() => handleInputFocus(result.index)"
                        @input="(value) => handleSearchInput(value, result.index)"
                        @keyup.enter="(event) => addNewTag(result.index, event)"
                        @blur="() => closeSuggestions(result.index)"
                        size="small"
                        :loading="isFetchingTags && !hasLoadedTags">
                        <template #prefix>
                          <el-icon v-if="isFetchingTags && !hasLoadedTags" class="is-loading">
                            <Loading />
                          </el-icon>
                        </template>
                      </el-input>
                      <el-button 
                        type="primary" 
                        size="small"
                        @click="() => addNewTag(result.index)"
                      >
                        Add new
                      </el-button>
                      </form>
                      
                      <!-- Tag suggestions dropdown -->
                      <div
                        v-show="showTagSuggestions.get(result.index) && getFilteredTags(searchQuery.get(result.index)).length > 0"
                        class="tag-suggestions-dropdown">
                        <template v-if="!isFetchingTags || hasLoadedTags">
                          <div v-for="tag in getFilteredTags(searchQuery.get(result.index))" :key="tag"
                            class="tag-suggestion-item" @mousedown="() => selectTag(result.index, tag)">
                            {{ tag }}
                          </div>
                        </template>
                        <div v-else class="loading-indicator">
                          Loading tags...
                        </div>
                      </div>
                    </div>
                  </div>
                </el-descriptions-item>

                <el-descriptions-item label="Concept Tags (for reference)">
                  <div class="tags-container">
                    <el-tag v-for="tag in result.tags.concept_tags" :key="tag" type="info"
                      class="tag-item draggable-tag" draggable="true"
                      @dragstart="handleDragStart($event, tag, 'concept')"
                      @dblclick="(event) => addNewTagDoubleClick(result.index, tag)">
                      {{ tag }}
                    </el-tag>
                  </div>
                </el-descriptions-item>

                <el-descriptions-item label="Person/Org Tags (for reference)">
                  <div class="tags-container">
                    <el-tag v-for="tag in result.tags.person_org_tags" :key="tag" type="info"
                      class="tag-item draggable-tag" draggable="true"
                      @dragstart="handleDragStart($event, tag, 'person_org')"
                      @dblclick="(event) => addNewTagDoubleClick(result.index, tag)">
                      {{ tag }}
                    </el-tag>
                  </div>
                </el-descriptions-item>

                <el-descriptions-item label="Time/Place Tags (for reference)">
                  <div class="tags-container">
                    <el-tag v-for="tag in result.tags.time_place_tags" :key="tag" type="info"
                      class="tag-item draggable-tag" draggable="true"
                      @dragstart="handleDragStart($event, tag, 'time_place')"
                      @dblclick="(event) => addNewTagDoubleClick(result.index, tag)">
                      {{ tag }}
                    </el-tag>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>
          </el-collapse>
        </el-col>
      </el-row>
      <!-- Pagination component -->
      <el-row justify="center" class="mt-4">
        <el-col :span="24">
          <el-pagination 
            v-model:current-page="currentPage" 
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]" 
            :total="results.length" 
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" 
            @current-change="handleCurrentChange" 
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import axios from 'axios'
import { debounce } from 'lodash'
import {
  ElDivider,
  ElRow,
  ElCol,
  ElButton,
  ElIcon,
  ElCollapse,
  ElCollapseItem,
  ElDescriptions,
  ElDescriptionsItem,
  ElText,
  ElTooltip,
  ElTag,
  ElInput,
  ElPagination,
  ElMessage
} from 'element-plus'
import { ZoomIn, ZoomOut, InfoFilled, Loading, CircleCheckFilled } from '@element-plus/icons-vue'

// Internal state for tag management and pagination
const deselectedTags = ref(new Set())
const newTags = ref(new Map())
const inputNewTagValue = ref('')
const activeNames = ref([])
const searchQuery = ref(new Map()) // Map to store search query for each result index
const showTagSuggestions = ref(new Map()) // Map to store dropdown state for each result index
const currentPage = ref(1)
const pageSize = ref(10)

// Review state tracking - maps result index to review status
const reviewedItems = ref(new Set())

// Props - only essential data from parent
const props = defineProps({
  results: {
    type: Array,
    required: true
  },
  allIndexedBiblioItems: {
    type: Array,
    required: true
  },
  screenIsPortrait: {
    type: Boolean,
    required: true
  },
  tagNames: {
    type: Array,
    required: true
  },
  allTagCandidates: {
    type: Array,
    required: true
  },
  apiAllTagsUrl: {
    type: String,
    required: true
  },
  isFetchingTags: {
    type: Boolean,
    required: true
  },
  hasLoadedTags: {
    type: Boolean,
    required: true
  }
})

// Computed property for pagination
const paginatedResults = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return props.results.slice(startIndex, endIndex)
})

// Emits - only essential events for parent communication
const emit = defineEmits([
  'tagsUpdated', // Emit when tag state changes that parent needs to know about
  'stateUpdated', // Emit when pagination or activeNames state changes
  'reviewStateUpdated' // Emit when review state changes
])

// Helper methods
const getAbstractByIndex = (index) => {
  const item = props.allIndexedBiblioItems.find(item => item.index === index)
  return item ? item.abstract : ''
}

const isTagDeselected = (resultIndex, tag) => {
  return deselectedTags.value.has(`${resultIndex}-${tag}`)
}

// Persistent cache for enhanced matched tags - survives batch processing
const enhancedMatchedTagsCache = ref(new Map())

// Function to process a single result's tags (extracted for reuse)
const processResultTags = (result) => {
  // If tag pool is not loaded yet, return basic tag structure without enhancement
  if (!props.hasLoadedTags || props.allTagCandidates.length === 0) {
    return result.tags.matched_tags.map(tagName => ({
      name: tagName,
      count: 0,
      category: 'Loading...',
      categoryClass: 5, // Use info color for loading state
      metadata: null
    }))
  }

  // Process ALL matched tags (including deselected ones)
  const enhancedTags = result.tags.matched_tags.map(tagName => {
    // Find matching tag metadata from allTagCandidates
    const matchingTag = props.allTagCandidates.find(t => t.name === tagName)

    // Determine category (with fallback)
    const category = matchingTag?.category || 'Uncategorized'

    // Get related citations count (with fallback)
    const count = matchingTag?.related_citations_count || 0

    // Determine category color class
    const categoryColorsDict = {"Concept": 1, "Persons": 2, "Institutions": 3, "Times and Places": 4, "Uncategorized": 5}

    return {
      name: tagName,
      count: count,
      category: category,
      categoryClass: categoryColorsDict[category],
      metadata: matchingTag
    }
  })

  // Group by category, then sort within each category by related_citations_count (descending)
  const tagsByCategory = {}
  enhancedTags.forEach(tag => {
    if (!tagsByCategory[tag.category]) {
      tagsByCategory[tag.category] = []
    }
    tagsByCategory[tag.category].push(tag)
  })

  // Sort tags within each category by count (descending), then by name
  Object.keys(tagsByCategory).forEach(category => {
    tagsByCategory[category].sort((a, b) => {
      const countDiff = (b.count || 0) - (a.count || 0)
      if (countDiff !== 0) return countDiff
      return a.name.localeCompare(b.name)
    })
  })

  // Flatten back to array, with categories in alphabetical order
  return Object.keys(tagsByCategory)
    .sort()
    .flatMap(category => tagsByCategory[category])
}

// Computed property that only processes new/changed results
const baseEnhancedMatchedTagsMap = computed(() => {
  const resultMap = new Map()

  // If tags are not loaded yet, clear cache to force reprocessing when they are loaded
  if (!props.hasLoadedTags) {
    // Return basic structure for all results while loading
    paginatedResults.value.forEach(result => {
      const basicTags = result.tags.matched_tags.map(tagName => ({
        name: tagName,
        count: 0,
        category: 'Loading...',
        categoryClass: 5,
        metadata: null
      }))
      resultMap.set(result.index, basicTags)
    })
    return resultMap
  }

  // If tags just finished loading, clear cache to force reprocessing with full metadata
  if (props.hasLoadedTags && enhancedMatchedTagsCache.value.size > 0) {
    const firstCachedResult = enhancedMatchedTagsCache.value.values().next().value
    if (firstCachedResult && firstCachedResult[0]?.category === 'Loading...') {
      enhancedMatchedTagsCache.value.clear()
    }
  }

  // Use cached results and process new ones
  const cachedResults = new Map(enhancedMatchedTagsCache.value)

  paginatedResults.value.forEach(result => {
    if (cachedResults.has(result.index)) {
      resultMap.set(result.index, cachedResults.get(result.index))
    } else {
      const processedTags = processResultTags(result)
      resultMap.set(result.index, processedTags)
      enhancedMatchedTagsCache.value.set(result.index, processedTags)
    }
  })

  return resultMap
})

// Efficient helper function that doesn't trigger unnecessary recalculations
const getEnhancedMatchedTags = (result) => {
  return baseEnhancedMatchedTagsMap.value.get(result.index) || []
}

// Pagination event handlers
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // Reset to first page when changing page size
  emitStateUpdated()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emitStateUpdated()
}

const toggleAllResults = () => {
  if (activeNames.value.length >= 1) {
    activeNames.value = []
  } else {
    activeNames.value = paginatedResults.value.map(result => result.index)
  }
  emitStateUpdated()
}

// Tag management functions
const toggleTag = (resultIndex, tag) => {
  const key = `${resultIndex}-${tag}`
  if (deselectedTags.value.has(key)) {
    deselectedTags.value.delete(key)
  } else {
    deselectedTags.value.add(key)
  }
  // Force reactivity update
  deselectedTags.value = new Set(deselectedTags.value)
  emitTagsUpdated()
}

const addNewTag = (resultIndex, event) => {
  const tag = inputNewTagValue.value.trim()
  if (!tag) return

  if (!newTags.value.has(resultIndex)) {
    newTags.value.set(resultIndex, new Set())
  }

  // Check if the tag is already in the Set for this resultIndex
  const existingTags = newTags.value.get(resultIndex);
  const isTagAlreadyAdded = Array.from(existingTags).some(
    (existingTag) => existingTag.text === tag
  );

  // Find the result by index
  const result = props.results.find(r => r.index === resultIndex)
  if (!result) {
    console.error('Result not found for index:', resultIndex)
    return
  }

  if (isTagAlreadyAdded || (result.tags.matched_tags.includes(tag) && !isTagDeselected(resultIndex, tag))) {
    ElMessage.warning('Tag already exists in matched tags.')
    return; // If the tag already exists, do not add it again
  }

  // Check if tag exists in tagNames
  const isExistingTag = props.tagNames.includes(tag)

  newTags.value.get(resultIndex).add({
    text: tag,
    isMatched: isExistingTag
  })
  // Force reactivity update
  newTags.value = new Map(newTags.value)
  inputNewTagValue.value = ''
  searchQuery.value.set(resultIndex, '')
  showTagSuggestions.value.set(resultIndex, false)
  emitTagsUpdated()
}

const addNewTagDoubleClick = (resultIndex, tag) => {
  if (!tag) return; // Guard clause for empty tags

  if (!newTags.value.has(resultIndex)) {
    newTags.value.set(resultIndex, new Set());
  }

  // Check if the tag is already in the Set for this resultIndex
  const existingTags = newTags.value.get(resultIndex);
  const isTagAlreadyAdded = Array.from(existingTags).some(
    (existingTag) => existingTag.text === tag
  );

  // Find the result by index
  const result = props.results.find(r => r.index === resultIndex)
  if (!result) {
    console.error('Result not found for index:', resultIndex)
    return
  }

  if (isTagAlreadyAdded || (result.tags.matched_tags.includes(tag) && !isTagDeselected(resultIndex, tag))) {
    ElMessage.warning('Tag already exists in matched tags.')
    return; // If the tag already exists, do not add it again
  }

  // Check if tag exists in tagNames
  const isExistingTag = props.tagNames.includes(tag);

  // Add the tag to the newTags Set
  newTags.value.get(resultIndex).add({
    text: tag,
    isMatched: isExistingTag
  })
  // Force reactivity update
  newTags.value = new Map(newTags.value)
  ElMessage.success('New tag added to matched tags!')
  emitTagsUpdated()
}

const removeNewTag = (resultIndex, tagText) => {
  const tags = newTags.value.get(resultIndex)
  if (tags) {
    for (const tag of tags) {
      if (tag.text === tagText) {
        tags.delete(tag)
        break
      }
    }
    // Force reactivity update
    newTags.value = new Map(newTags.value)
    emitTagsUpdated()
  }
}

// Drag and drop functions
const handleDragStart = (event, tag, sourceSection) => {
  event.dataTransfer.setData('text/plain', JSON.stringify({
    tag,
    sourceSection
  }))
}

const handleDragOver = (event) => {
  event.preventDefault()
}

const handleDrop = (event, resultIndex) => {
  event.preventDefault()
  const data = JSON.parse(event.dataTransfer.getData('text/plain'))
  const { tag, sourceSection } = data

  // Find the result by index
  const result = props.results.find(r => r.index === resultIndex)
  if (!result) {
    console.error('Result not found for index:', resultIndex)
    return
  }

  // Add to matched tags if not already present
  if (!result.tags.matched_tags.includes(tag) || isTagDeselected(resultIndex, tag)) {
    if (!newTags.value.has(resultIndex)) {
      newTags.value.set(resultIndex, new Set())
    }

    // Check if the tag is already in the Set for this resultIndex
    const existingTags = newTags.value.get(resultIndex);
    const isTagAlreadyAdded = Array.from(existingTags).some(
      (existingTag) => existingTag.text === tag
    );

    if (isTagAlreadyAdded) {
      ElMessage.warning('Tag already exists in matched tags.')
      return; // If the tag already exists, do not add it again
    }

    const isExistingTag = props.tagNames.includes(tag)

    newTags.value.get(resultIndex).add({
      text: tag,
      isMatched: isExistingTag,
    })
    // Force reactivity update
    newTags.value = new Map(newTags.value)
    emitTagsUpdated()
  } else {
    ElMessage.warning('Tag already exists in matched tags.');
    return;
  }
}

// Tag suggestion and fetching logic - delegate to parent
const fetchAllTags = async () => {
  // Since tag fetching is handled by parent, we don't need to do anything here
  // The parent will handle the actual fetching when needed
}

// Debounced filter function for smooth typing experience
const debouncedFilter = debounce((query, resultIndex) => {
  if (!query || query.length < 2) {
    showTagSuggestions.value.set(resultIndex, false)
    return
  }
  showTagSuggestions.value.set(resultIndex, true)
}, 300)

// Get filtered tags based on search query
const getFilteredTags = (query) => {
  if (!query || query.length < 2) return []

  const searchTerm = query.toLowerCase()
  return props.tagNames
    .filter(tag => tag.toLowerCase().includes(searchTerm))
    .sort((a, b) => {
      const aLower = a.toLowerCase()
      const bLower = b.toLowerCase()
      const aStartsWith = aLower.startsWith(searchTerm)
      const bStartsWith = bLower.startsWith(searchTerm)
      if (aStartsWith && !bStartsWith) return -1
      if (!aStartsWith && bStartsWith) return 1
      return aLower.indexOf(searchTerm) - bLower.indexOf(searchTerm)
    })
    .slice(0, 40) // Limit to first 40 matches
}

// Handle input focus
const handleInputFocus = async (resultIndex) => {
  // Tag loading is handled by parent component
  showTagSuggestions.value.set(resultIndex, true)
}

// Handle input changes
const handleSearchInput = (value, resultIndex) => {
  searchQuery.value.set(resultIndex, value) // Update search query for filtering
  debouncedFilter(value, resultIndex)
}

// Handle tag selection
const selectTag = (resultIndex, tag) => {
  if (!newTags.value.has(resultIndex)) {
    newTags.value.set(resultIndex, new Set())
  }

  // Check if the tag is already in the Set for this resultIndex
  const existingTags = newTags.value.get(resultIndex);
  const isTagAlreadyAdded = Array.from(existingTags).some(
    (existingTag) => existingTag.text === tag
  );

  // Find the result by index
  const result = props.results.find(r => r.index === resultIndex)
  if (!result) {
    console.error('Result not found for index:', resultIndex)
    return
  }

  if (isTagAlreadyAdded || (result.tags.matched_tags.includes(tag) && !isTagDeselected(resultIndex, tag))) {
    ElMessage.warning('Tag already exists in matched tags.')
    return; // If the tag already exists, do not add it again
  }

  newTags.value.get(resultIndex).add({
    text: tag,
    isMatched: true
  })
  // Force reactivity update
  newTags.value = new Map(newTags.value)
  inputNewTagValue.value = ''
  searchQuery.value.set(resultIndex, '')
  showTagSuggestions.value.set(resultIndex, false)
  emitTagsUpdated()
}

// Close suggestions dropdown
const closeSuggestions = (resultIndex) => {
  setTimeout(() => {
    showTagSuggestions.value.set(resultIndex, false)
  }, 200)
}

// Helper function to emit tags updated event
const emitTagsUpdated = () => {
  emit('tagsUpdated', {
    deselectedTags: deselectedTags.value,
    newTags: newTags.value
  })
}

// Helper function to emit state updated event
const emitStateUpdated = () => {
  emit('stateUpdated', {
    currentPage: currentPage.value,
    pageSize: pageSize.value,
    activeNames: activeNames.value
  })
}

// Review functionality methods
const toggleReviewStatus = (resultIndex) => {
  if (reviewedItems.value.has(resultIndex)) {
    reviewedItems.value.delete(resultIndex)
  } else {
    reviewedItems.value.add(resultIndex)
  }
  // Emit the updated review state to parent
  emit('reviewStateUpdated', reviewedItems.value)
}

const isItemReviewed = (resultIndex) => {
  return reviewedItems.value.has(resultIndex)
}

// Method to reset internal state (called by parent when clearing all data)
const resetInternalState = () => {
  deselectedTags.value = new Set()
  newTags.value = new Map()
  inputNewTagValue.value = ''
  activeNames.value = []
  searchQuery.value = new Map()
  showTagSuggestions.value = new Map()
  currentPage.value = 1
  pageSize.value = 10
  // Clear the enhanced tags cache
  enhancedMatchedTagsCache.value = new Map()
  // Clear review state
  reviewedItems.value = new Set()
}

// Expose methods for parent component access
defineExpose({
  toggleAllResults,
  resetInternalState
})
</script>

<style scoped>
.results {
  margin-top: 20px;
}

.button-container {
  margin-bottom: 20px;
}

.el-pagination {
  flex-wrap: wrap;
  gap: 5px;
}

.truncate-title-results {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.droppable-area {
  min-height: 40px;
  padding: 8px;
  border: 2px dashed transparent;
  border-radius: 4px;
  transition: all 0.3s;
}

.droppable-area:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.el-tag {
  white-space: normal; /* Allow text to wrap */
  word-break: break-word; /* Break long words if necessary */
  min-height: 1.5rem;
  height: fit-content;
  max-width: 100%; /* Ensure the tag doesn't overflow its container */
}

.tag-item {
  margin: 0px;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.deselected-tag {
  opacity: 0.5;
  text-decoration: line-through;
}

.draggable-tag {
  cursor: move;
  transition: transform 0.2s;
}

.draggable-tag:hover {
  transform: scale(1.05);
}

.draggable-tag:active {
  cursor: grabbing;
}

.tag-input-container {
  position: relative;
  display: inline-block;
}

.tag-input-container form {
  display: flex;
  align-items: center; /* Vertically aligns the input and button */
  gap: 4px; /* Creates an 8px space between the items */
}

.tag-input-container .el-input {
  flex-grow: 1; /* This is the key: it makes the input expand to fill remaining space */
  min-width: 200px; /* Optional: prevent the input from becoming too small */
}

.tag-input {
  width: 100%;
}

.tag-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.tag-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.tag-suggestion-item:hover {
  background-color: var(--el-bg-color-overlay);
}

.tag-suggestion-item:last-child {
  border-bottom: none;
}

.loading-indicator {
  padding: 8px 12px;
  text-align: center;
  color: var(--el-text-color-secondary);
}

.tag-loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 8px;
}

/* Category-based tag colors */
.el-tag.category-1 {
  background-color: #4272a1;
  border-color: #4272a1;
  color: #ffffff;
}

.el-tag.category-2 {
  background-color: #4191a6;
  border-color: #4191a6;
  color: #ffffff;
}

.el-tag.category-3 {
  background-color: #4191a6;
  border-color: #4191a6;
  color: #ffffff;
}

.el-tag.category-4 {
  background-color: #67c4a5;
  border-color: #67c4a5;
  color: #ffffff;
}

.el-tag.category-5 {
  background-color: var(--el-color-info-light-9);
  border-color: var(--el-color-info-light-7);
  color: var(--el-color-info-dark-2);
}

/* Review functionality styles */
.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 10px;
}

.review-checkmark {
  color: var(--el-color-success);
  margin-left: 8px;
  font-size: 16px;
}

.review-button {
  margin-left: 10px;
  flex-shrink: 0;
}

.truncate-title-results {
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}
</style>

